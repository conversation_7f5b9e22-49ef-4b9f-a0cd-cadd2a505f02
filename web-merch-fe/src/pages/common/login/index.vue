<template>
  <view class="login-container">
    <!-- 导航栏占位 -->
    <wd-navbar custom-class="!bg-transparent" :bordered="false" />

    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1" />
      <view class="bg-circle bg-circle-2" />
      <view class="bg-circle bg-circle-3" />
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- logo区域 -->
      <view class="logo-section">
        <view class="logo-wrapper">
          <wd-img :src="require('@/static/images/logo.jpg')" width="120rpx" height="120rpx" radius="16" />
        </view>
        <view class="welcome-text">
          <text class="welcome-subtitle">
            请登录您的账户
          </text>
        </view>
      </view>

      <!-- 用户类型选择 -->
      <view class="user-type-section">
        <wd-segmented
          v-model:value="currentUserType"
          :options="userTypeOptions"
          :vibrate-short="true"
          @change="onChangeUserType"
        />
      </view>

      <!-- 登录表单 -->
      <view class="form-section">
        <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
          <view class="input-group">
            <wd-input
              v-model="form.account"
              prop="account"
              placeholder="请输入账号"
              label-width="50rpx"
              type="number"
              center use-label-slot use-suffix-slot
              custom-class="custom-input"
            >
              <template #label>
                <view class="input-icon">
                  <wd-icon name="user" size="20px" color="#517cf0" />
                </view>
              </template>
              <template #suffix>
                <!-- 打开账号列表 -->
                <wd-icon
                  v-if="accountStoreList.length"
                  name="user-avatar" size="18px" color="#9ca3af"
                  @click="showSelectAccount = true"
                />
              </template>
            </wd-input>
          </view>

          <view class="input-group">
            <wd-input
              v-model="form.password"
              prop="password"
              placeholder="请输入密码"
              label-width="50rpx"
              center use-label-slot
              show-password
              custom-class="custom-input"
            >
              <template #label>
                <view class="input-icon">
                  <wd-icon name="lock-on" size="20px" color="#517cf0" />
                </view>
              </template>
            </wd-input>
          </view>
        </wd-form>

        <!-- 功能选项 -->
        <view class="options-section">
          <wd-checkbox v-model="isRemember" custom-label-class="custom-checkbox-label">
            <text>记住密码</text>
          </wd-checkbox>
          <view class="forgot-password" @click="toResetPwd">
            <text>忘记密码?</text>
          </view>
        </view>

        <!-- 协议同意 -->
        <view class="agreement-section">
          <wd-checkbox v-model="isAgree" custom-label-class="custom-checkbox-label">
            <text class="agreement-text">
              我已阅读并同意
            </text>
            <text class="agreement-link" @click.stop="toService">
              《服务协议》
            </text>
            <text class="agreement-text">
              和
            </text>
            <text class="agreement-link" @click.stop="toPrivacy">
              《隐私权规则》
            </text>
          </wd-checkbox>
        </view>

        <!-- 登录按钮 -->
        <view class="login-button-section">
          <wd-button
            type="primary"
            size="large"
            block
            custom-class="login-button"
            @click="save"
          >
            <text class="login-button-text">
              登录
            </text>
          </wd-button>
        </view>
      </view>

      <!-- #ifdef MP-WEIXIN -->
      <view class="no-login-section">
        <text class="no-login-text" @click="handleNoLogin">
          暂不登录
        </text>
      </view>
      <!-- #endif -->
    </view>

    <!-- 选择账号登录弹框 -->
    <wd-action-sheet v-model="showSelectAccount" title="选择账号登录" custom-class="account-sheet">
      <view class="account-list">
        <wd-cell-group border>
          <wd-cell
            v-for="(item, key) in accountStoreList"
            :key="key"
            :title="item.account"
            center clickable
            custom-class="account-item"
            @click="changeAccount(item)"
          >
            <wd-icon
              name="delete"
              size="16px"
              color="#ef4444"
              @click.stop="deleteAccount(item.account)"
            />
          </wd-cell>
        </wd-cell-group>
      </view>
      <view class="account-sheet-footer">
        <wd-button
          type="info"
          block
          size="large"
          custom-class="cancel-button"
          @click="showSelectAccount = false"
        >
          取消
        </wd-button>
      </view>
    </wd-action-sheet>

    <wd-toast />

    <!-- 协议弹框 -->
    <wd-message-box selector="wd-agreement-box" custom-class="agreement-message-box">
      <text>请先阅读并同意</text>
      <text class="agreement-link" @click.stop="toService">
        《服务协议》
      </text>
      <text>和</text>
      <text class="agreement-link" @click.stop="toPrivacy">
        《隐私权规则》
      </text>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { sm4 } from 'sm-crypto';
import { useToast } from 'wot-design-uni';
import type { IRecordItem, LoginForm } from './type';
import { UserApi } from '@/api/user/index';
import { UserApi as UserApiOrg } from '@/api-org/user/index';
import storage from '@/utils/storage';
import { Dialog, buildUrlWithParams, clearToken, setToken } from '@/utils';
import { useUserStore } from '@/store';
import { MerchApi } from '@/api/merch';

const toast = useToast();

// 选择登录用户类型
const currentUserType = ref('商户');
const userTypeOptions = [
  {
    mark: 'org',
    value: '机构',
    disabled: false,
  },
  {
    mark: 'merch',
    value: '商户',
    disabled: false,
  },
];
const currentUserTypeMark = computed(() => {
  return userTypeOptions.find(item => item.value === currentUserType.value)?.mark;
});

function onChangeUserType() {
  getLoginAccount();
}

// 表单
const formRef = ref<FormInstance | null>(null);

const form: LoginForm = reactive({
  account: '',
  password: '',
  type: 0,
});
// 规则
const rules: FormRules = {
  account: [{ required: true, message: '请输入账号' }],
  password: [{ required: true, message: '请输入密码' }],
};

// 选择账号登录
const showSelectAccount = ref(false);
const accountStoreList = ref<IRecordItem[]>([]);

// 同意协议
const isAgree = ref(false);
// 记住密码
const isRemember = ref(true);

onLoad(() => {
  clearToken();
});

onShow(() => {
  getUserType();
  getLoginAccount();
});

/**
 * 提交
 */
async function save() {
  // 验证表单
  const { valid } = await formRef.value!.validate();
  if (!valid)
    return;

  if (!isAgree.value) {
    return toast.info('请先阅读并同意服务条款');
  }

  let data;

  switch (currentUserTypeMark.value) {
    case 'org':
      data = await UserApiOrg.login(form);
      break;
    case 'merch':
      data = await UserApi.login(form);
      break;
  }

  // 存储token
  setToken(data?.token);

  // 存储登录用户类型
  useUserStore().setUserType(currentUserTypeMark.value as any);

  // 存储用户信息
  const userInfo = { ...data?.loginUser, ...data?.loginUser?.simpleUserInfo };
  delete (userInfo.simpleUserInfo);
  useUserStore().setInfo(userInfo);

  // 存储登录账号
  if (isRemember.value) {
    saveLoginAccount();
  }

  if (currentUserTypeMark.value === 'merch') {
    // 查询商户状态
    queryMerchStatus();
  }
  else if (currentUserTypeMark.value === 'org') {
    queryOrgUserInfo();
  }
}

async function queryOrgUserInfo() {
  const data = await UserApiOrg.getOrgUserInfo();

  useUserStore().setInfo({
    companyName: data.companyName,
    legalName: data.legalName,
  });

  uni.switchTab({ url: '/pages/tab/home/<USER>' });
}

/**
 * 查询商户状态
 */
async function queryMerchStatus() {
  const data = await MerchApi.queryMerchInfo();
  const { merchant, merchantDetail } = data || {};

  // 存储法人信息
  useUserStore().setInfo({
    legalName: merchantDetail?.legalName || '',
  });

  // 0-初始状态 1-人工审核 2-审核不通过 3-审核通过 4-入网成功
  switch (merchant.authStatus) {
    case 0:
      uni.navigateTo({ url: '/pages/report/merch-auth/auth-micro-merch/index' });
      break;
    case 1:
    case 2:
    case 3:
      uni.navigateTo({ url: '/pages/report/merch-auth/auth-result' });
      break;
    case 4:
      if (merchant.haveElecSignatureStatus) {
        uni.navigateTo({ url: '/pages/report/merch-signature/index' });
      }
      else {
        uni.switchTab({ url: '/pages/tab/home/<USER>' });
      }

      uni.switchTab({ url: '/pages/tab/home/<USER>' });
      break;
    default:
      Dialog('商户状态异常，请联系管理员');
      break;
  }
}

// 持久存储key
const LoginRecordStoreKey = computed(() => {
  if (currentUserTypeMark.value === 'org') {
    return 'loginRecordOrg';
  }
  return 'loginRecord';
});
// 密码处理
const secretPwd = {
  key: '0123456789abcdeffedcba9876543210',
  encrypt: (password: string) => {
    return sm4.encrypt(password, secretPwd.key);
  },
  decrypt: (password: string) => {
    return sm4.decrypt(password, secretPwd.key);
  },
};

/** 保存登录账号 */
function saveLoginAccount() {
  const recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey.value) || [];
  const { account, password } = form;
  const recordItem: IRecordItem = {
    account,
    password: secretPwd.encrypt(password),
  };

  // 如果存在先删除, 保证记录唯一且最新
  const hadIndex = recordList.findIndex(item => item.account === account);
  if (hadIndex !== -1) {
    recordList.splice(hadIndex, 1);
  }

  // 插入到最前面
  recordList.unshift(recordItem);

  storage.setJSON(LoginRecordStoreKey.value, recordList);
}

/** 获取登录账号列表 */
function getLoginAccount() {
  const recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey.value) || [];

  recordList.forEach((item) => {
    item.password = secretPwd.decrypt(item.password);
  });

  accountStoreList.value = recordList;

  if (recordList[0]) {
    const { account, password } = recordList[0];
    form.account = account;
    form.password = password;
    isAgree.value = true;
  }
  else {
    form.account = '';
    form.password = '';
  }
}

/** 切换账号 */
function changeAccount(item: IRecordItem) {
  const { account, password } = item;
  form.account = account;
  form.password = password;
  showSelectAccount.value = false;
}

/** 删除账号 */
function deleteAccount(account: string) {
  let recordList: IRecordItem[] = storage.getJSON(LoginRecordStoreKey.value) || [];
  recordList = recordList.filter(item => item.account !== account);
  storage.setJSON(LoginRecordStoreKey.value, recordList);
  getLoginAccount();
}

function getUserType() {
  const userType = useUserStore().userType;
  const item = userTypeOptions.find(item => item.mark === userType);
  currentUserType.value = item?.value as string;
}

function toResetPwd() {
  if (currentUserTypeMark.value === 'org') {
    uni.navigateTo({ url: '/pages-org/settings/reset-login-pwd' });
  }
  else {
    uni.navigateTo({ url: '/pages/settings/reset-login-pwd' });
  }
}

function handleNoLogin() {
  uni.redirectTo({ url: '/pages/common/tourist/index' });
}

function toPrivacy() {
  const url = buildUrlWithParams('/pages/common/webview/index', {
    url: 'https://wsapp.xnwcloud.com/api/app/privacy',
    title: '隐私权规则',
  });
  uni.navigateTo({ url });
}

function toService() {
  const url = buildUrlWithParams('/pages/common/webview/index', {
    url: 'https://wsapp.xnwcloud.com/api/app/userService',
    title: '服务协议',
  });
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
// 主容器样式
.login-container {
  position: relative;
  display: flex;
  overflow: hidden;
  min-height: 100vh;
  background: linear-gradient(160deg, #5b8def 0%, #4a73e8 30%, #3d5afe 70%, #2962ff 100%);
  flex-direction: column;
}

// 背景装饰
.bg-decoration {
  position: absolute;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  background: radial-gradient(circle, rgb(255 255 255 / 12%) 0%, rgb(255 255 255 / 4%) 70%, transparent 100%);
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 50%;
  backdrop-filter: blur(20px);
}

.bg-circle-1 {
  top: -150rpx;
  right: -150rpx;
  width: 300rpx;
  height: 300rpx;
  animation: float 6s ease-in-out infinite;
}

.bg-circle-2 {
  bottom: 200rpx;
  left: -100rpx;
  width: 200rpx;
  height: 200rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.bg-circle-3 {
  top: 50%;
  right: -75rpx;
  width: 150rpx;
  height: 150rpx;
  animation: float 10s ease-in-out infinite;
}

// 添加渐变叠加层，增强视觉效果
.login-container::before {
  position: absolute;
  inset: 0;
  z-index: 1;
  background: radial-gradient(ellipse at top, transparent 0%, rgb(0 0 0 / 3%) 100%);
  content: '';
  pointer-events: none;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

// 主要内容区域
.main-content {
  position: relative;
  z-index: 1;
  display: flex;
  padding: 40rpx;
 flex: 1;
  flex-direction: column;
}

// Logo区域
.logo-section {
  margin: 60rpx 0;
  text-align: center;
}

.logo-wrapper {
  display: inline-block;
  padding: 5rpx;
  margin-bottom: 40rpx;
  font-size: 0;
  background: linear-gradient(145deg, rgb(255 255 255 / 18%) 0%, rgb(255 255 255 / 8%) 100%);
  border: 1px solid rgb(255 255 255 / 25%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgb(0 0 0 / 6%), inset 0 1rpx 0 rgb(255 255 255 / 30%);
  backdrop-filter: blur(25px);
}

.welcome-text {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.welcome-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 8rpx rgb(0 0 0 / 10%);
}

.welcome-subtitle {
  font-size: 28rpx;
  color: rgb(255 255 255 / 80%);
  font-weight: 400;
}

// 用户类型选择
.user-type-section {
  margin-bottom: 50rpx;

  :deep(.wd-segmented) {
    background: linear-gradient(145deg, rgb(255 255 255 / 15%) 0%, rgb(255 255 255 / 8%) 100%);
    border: 1px solid rgb(255 255 255 / 30%);
    border-radius: 16rpx;
    backdrop-filter: blur(25px);
    box-shadow: 0 4rpx 16rpx rgb(0 0 0 / 5%), inset 0 1rpx 0 rgb(255 255 255 / 25%);

    .wd-segmented__item {
      color: rgb(255 255 255 / 80%);
      font-weight: 500;

      &.is-active {
        color: #517cf0;
        background: rgb(255 255 255 / 90%);
        box-shadow: 0 4rpx 16rpx rgb(0 0 0 / 10%);
      }
    }
  }
}

// 表单区域
.form-section {
  position: relative;
  z-index: 2;
  padding: 50rpx 30rpx 40rpx;
  margin: 0 -40rpx -40rpx;
  background: linear-gradient(180deg, rgb(255 255 255 / 98%) 0%, rgb(255 255 255 / 95%) 100%);
  border-radius: 32rpx 32rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgb(0 0 0 / 6%), 0 -2rpx 8rpx rgb(0 0 0 / 4%);
  flex: 1;
  backdrop-filter: blur(30px);
  border-top: 1px solid rgb(255 255 255 / 40%);
}

// 输入框组
.input-group {
  margin-bottom: 32rpx;

  :deep(.custom-input) {
    &.is-cell {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &:focus-within {
        background: #fff;
        border-color: #517cf0;
        box-shadow: 0 0 0 6rpx rgb(81 124 240 / 10%);
      }
    }

    .wd-input__label {
      padding-left: 0;

      &::after {
        display: none;
      }
    }

    .wd-input__inner {
      font-size: 32rpx;
      color: #1f2937;

      &::placeholder {
        color: #9ca3af;
      }
    }
  }
}

.input-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40rpx;
  height: 40rpx;
  background: rgb(81 124 240 / 10%);
  border-radius: 12rpx;
}

// 功能选项
.options-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -10rpx;
  margin-bottom: 70rpx;

  :deep(.custom-checkbox-label) {
    font-size: 28rpx;
    color: #6b7280;
  }
}

.forgot-password {
  text {
    font-size: 26rpx;
    color: #517cf0;
  }
}

// 协议区域
.agreement-section {
  margin-bottom: 30rpx;

  :deep(.custom-checkbox-label) {
    font-size: 26rpx;
    color: #6b7280;
    line-height: 1.6;
  }
}

.agreement-text {
  color: #6b7280;
}

.agreement-link {
  color: #517cf0;
}

// 登录按钮
.login-button-section {
  margin-bottom: 40rpx;

  :deep(.login-button) {
    height: 90rpx;
    background: linear-gradient(135deg, #517cf0 0%, #254bb2 100%);
    border: none;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 24rpx rgb(81 124 240 / 30%);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 16rpx rgb(81 124 240 / 30%);
    }
  }
}

.login-button-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

// 暂不登录
.no-login-section {
  margin-top: 40rpx;
  text-align: center;
}

.no-login-text {
  font-size: 28rpx;
  text-decoration: underline;
  color: #9ca3af;
}

// 账号选择弹框
:deep(.account-sheet) {
  .wd-action-sheet__header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }
}

.account-list {
  overflow-y: auto;
  max-height: 400rpx;

  :deep(.account-item) {
    border-bottom: 1px solid #f1f5f9;

    &:last-child {
      border-bottom: none;
    }
  }
}

.account-sheet-footer {
  padding: 30rpx;
  background: #f8fafc;

  :deep(.cancel-button) {
    color: #64748b;
    background: #e2e8f0;
    border: none;
    border-radius: 12rpx;
  }
}

// 协议弹框
:deep(.agreement-message-box) {
  .wd-message-box__content {
    text-align: left;
    line-height: 1.6;
  }

  .agreement-link {
    color: #517cf0;
    font-weight: 500;
  }
}
</style>
